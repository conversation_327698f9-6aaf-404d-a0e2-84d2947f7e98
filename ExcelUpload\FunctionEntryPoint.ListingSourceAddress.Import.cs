using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Helper;
using Lrb.Application.ListingManagement.Web.Specs;
using Lrb.Application.Property.Web;
using Lrb.Application.Utils;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auth;
using Newtonsoft.Json;
using System.Collections.Concurrent;
using System.Data;

namespace ExcelUpload
{
    public partial class FunctionEntryPoint
    {
        public async Task ListingSourceAddressHandler(InputPayload input)
        {
            CancellationToken cancellationToken = CancellationToken.None;
            var tracker = await _listingSourceAddresstrackerRepo.FirstOrDefaultAsync(new BulkListingSourceAddressTrackerSpecs(input.TrackerId));
            try
            {
                if (tracker != null)
                {
                    tracker.MappedColumnsData = tracker.MappedColumnsData?.ToDictionary(i => i.Key, j => j.Value?.Trim() ?? string.Empty);
                    tracker.Status = UploadStatus.Started;
                    tracker.LastModifiedBy = input.CurrentUserId;
                    tracker.CreatedBy = input.CurrentUserId;

                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
                    Console.WriteLine($"FunctionEntryPoint() -> ListingSourceAddressUploadTracker Updated Status: {tracker.Status} \n {JsonConvert.SerializeObject(tracker)}");

                    #region Convert to Datatable

                    Stream fileStream = await _blobStorageService.GetObjectAsync(_blobStorageService?.BucketName ?? "leadrat-black", tracker.S3BucketKey);
                    DataTable dataTable = new();
                    if (tracker.S3BucketKey.Split('.').LastOrDefault() == "csv")
                    {
                        using MemoryStream memoryStream = new();
                        fileStream.CopyTo(memoryStream);
                        dataTable = CSVHelper.CSVToDataTable(memoryStream);
                    }
                    else
                    {
                        dataTable = EPPlusExcelHelper.ConvertExcelToDataTable(fileStream, tracker.SheetName);
                    }

                    List<InvalidProspect> invalids = new();
                    int totalRows = dataTable.Rows.Count;
                    for (int i = totalRows - 1; i >= 0; i--)
                    {
                        var row = dataTable.Rows[i];
                        if (row.ItemArray.All(i => string.IsNullOrEmpty(i.ToString())))
                        {
                            row.Delete();
                        }
                    }
                    if (dataTable.Rows.Count <= 0)
                    {
                        throw new Exception("Excel sheet is empty. Please fill some data in the excel sheet template.");
                    }
                    totalRows = dataTable.Rows.Count;
                    Console.WriteLine($"handler() -> Total Rows in the Excel: {dataTable.Rows.Count}");

                    #endregion

                    #region Fetch All requires data
                    var listingSource = await _customListingSource.FirstOrDefaultAsync(new GetAllListingSourceByIds(tracker.ListingSourceId ?? Guid.Empty));
                    #endregion

                    var listingSourceAddresses = dataTable.ConvertToListingSourceAddress(tracker.MappedColumnsData, listingSource);
                    listingSourceAddresses.ForEach(i => i.SetListingSourceAddress(tracker.MappedColumnsData, input.CurrentUserId));

                    #region Remove Duplicates - Optimized Bulk Check
                    Console.WriteLine($"handler() -> Starting duplicate check for {listingSourceAddresses.Count} addresses...");

                    // Ensure ListingSourceId is set properly for all addresses
                    foreach (var address in listingSourceAddresses)
                    {
                        if (address.ListingSourceId == null || address.ListingSourceId == Guid.Empty)
                        {
                            address.ListingSourceId = listingSource?.Id ?? Guid.Empty;
                        }
                    }

                    // Step 1: Remove duplicates within the current batch first (in-memory operation)
                    var uniqueInBatch = new List<ListingSourceAddress>();
                    var seenInBatch = new HashSet<string>();
                    int batchDuplicateCount = 0;

                    foreach (var address in listingSourceAddresses)
                    {
                        // Create a unique key for comparison - use ListingSourceId instead of ListingSource.Id
                        var key = $"{address.TowerName?.Trim()?.ToLower() ?? ""}|{address.SubCommunity?.Trim()?.ToLower() ?? ""}|{address.Community?.Trim()?.ToLower() ?? ""}|{address.City?.Trim()?.ToLower() ?? ""}|{address.ListingSourceId ?? Guid.Empty}";

                        if (!seenInBatch.Contains(key))
                        {
                            seenInBatch.Add(key);
                            uniqueInBatch.Add(address);
                        }
                        else
                        {
                            batchDuplicateCount++;
                            Console.WriteLine($"handler() -> Batch duplicate found: {address.TowerName}, {address.SubCommunity}, {address.Community}, {address.City}");
                        }
                    }

                    Console.WriteLine($"handler() -> After batch deduplication: {uniqueInBatch.Count} unique, {batchDuplicateCount} batch duplicates");

                    // Step 2: Bulk check against database for remaining unique addresses
                    var uniqueAddresses = new List<ListingSourceAddress>();
                    int dbDuplicateCount = 0;

                    if (uniqueInBatch.Count > 0)
                    {
                        // Get all existing addresses for this listing source in one query
                        var existingAddresses = await _listingSourceAddressRepo.ListAsync(
                            new GetListingSourceAddressBySourceIdSpecs(listingSource?.Id ?? Guid.Empty),
                            cancellationToken);

                        Console.WriteLine($"handler() -> Found {existingAddresses.Count} existing addresses in database for listing source {listingSource?.Id}");

                        // Create a HashSet for fast lookup of existing addresses
                        var existingKeys = new HashSet<string>();
                        foreach (var existing in existingAddresses)
                        {
                            var key = $"{existing.TowerName?.Trim()?.ToLower() ?? ""}|{existing.SubCommunity?.Trim()?.ToLower() ?? ""}|{existing.Community?.Trim()?.ToLower() ?? ""}|{existing.City?.Trim()?.ToLower() ?? ""}|{existing.ListingSourceId ?? Guid.Empty}";
                            existingKeys.Add(key);
                        }

                        // Check each unique address against existing ones
                        foreach (var address in uniqueInBatch)
                        {
                            var key = $"{address.TowerName?.Trim()?.ToLower() ?? ""}|{address.SubCommunity?.Trim()?.ToLower() ?? ""}|{address.Community?.Trim()?.ToLower() ?? ""}|{address.City?.Trim()?.ToLower() ?? ""}|{address.ListingSourceId ?? Guid.Empty}";

                            if (!existingKeys.Contains(key))
                            {
                                uniqueAddresses.Add(address);
                            }
                            else
                            {
                                dbDuplicateCount++;
                                Console.WriteLine($"handler() -> Database duplicate found: {address.TowerName}, {address.SubCommunity}, {address.Community}, {address.City}");
                            }
                        }
                    }

                    int totalDuplicateCount = batchDuplicateCount + dbDuplicateCount;
                    Console.WriteLine($"handler() -> Final result: {uniqueAddresses.Count} unique addresses to insert, {dbDuplicateCount} database duplicates REMOVED, {batchDuplicateCount} batch duplicates REMOVED, {totalDuplicateCount} total duplicates FILTERED OUT");
                    #endregion

                    tracker.Status = UploadStatus.InProgress;
                    tracker.TotalCount = totalRows;
                    tracker.DuplicateCount = totalDuplicateCount;
                    //tracker.InvalidCount = invalidChannelPartners.Count;
                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);

                    if (uniqueAddresses.Count > 0)
                    {
                        int listingSourceAddressChunks = uniqueAddresses.Count > 5000 ? 5000 : uniqueAddresses.Count;
                        var chunks = uniqueAddresses.Chunk(listingSourceAddressChunks).Select(i => new ConcurrentBag<Lrb.Domain.Entities.ListingSourceAddress>(i));
                        var tenantInfo = await _tenantIndependentRepo.GetTenantInfoAsync(input.TenantId);
                        var chunkIndex = 1;
                        var currentUserId = _currentUser.GetUserId();
                        if (currentUserId == Guid.Empty)
                        {
                            currentUserId = input.CurrentUserId;
                        }
                        foreach (var chunk in chunks.ToList())
                        {
                            var backgroundDto = new BulkAddressUploadBackgroundDto()
                            {
                                TrackerId = tracker?.Id ?? Guid.Empty,
                                TenantInfoDto = tenantInfo,
                                CancellationToken = CancellationToken.None,
                                ListingSourceAddresses = new(chunk),
                                CurrentUserId = currentUserId,
                            };
                            if (chunkIndex == chunks.Count())
                            {
                                backgroundDto.IsLastChunk = true;
                            }
                            await ExecuteDBOperationsAsync(backgroundDto);
                            chunkIndex++;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "ListingSourceAddressHandler -> ListingSourceHandler()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                Console.WriteLine($"handler() -> Exception:  {JsonConvert.SerializeObject(ex, settings: new() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
        }

        public async Task ExecuteDBOperationsAsync(BulkAddressUploadBackgroundDto dto)
        {
            ListingSourceAddress listingAddress = new();
            var tracker = await _listingSourceAddresstrackerRepo.FirstOrDefaultAsync(new BulkListingSourceAddressTrackerSpecs(dto.TrackerId));
            try
            {
                await _listingSourceAddressRepo.AddRangeAsync(dto.ListingSourceAddresses);
                if (tracker != null)
                {
                    tracker.TotalUploadedCount += dto.ListingSourceAddresses.Count;
                    if (dto.IsLastChunk)
                    {
                        tracker.Status = UploadStatus.Completed;
                    }
                    tracker.LastModifiedBy = dto.CurrentUserId;
                    tracker.CreatedBy = dto.CurrentUserId;
                    await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine($"ExecuteDBOperationsAsync() -> Exception: {JsonConvert.SerializeObject(e)}");
                if (tracker.TotalUploadedCount == tracker.InvalidCount)
                {
                    tracker.Status = UploadStatus.Completed;
                }
                else
                {
                    tracker.Status = UploadStatus.Failed;
                    tracker.Message = e?.InnerException?.Message ?? e?.Message;
                }
                tracker.LastModifiedBy = dto.CurrentUserId;
                tracker.CreatedBy = dto.CurrentUserId;
                await _listingSourceAddresstrackerRepo.UpdateAsync(tracker);
            }
        }
    }
}
